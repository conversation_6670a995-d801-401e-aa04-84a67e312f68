<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 24px;
            width: 100%;
            max-width: 400px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 24px;
        }
        
        .header h1 {
            color: #1f2937;
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .header p {
            color: #6b7280;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        
        .color-input {
            height: 40px;
            padding: 4px;
        }
        
        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }
        
        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #4f46e5;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4338ca;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 图片生成器</h1>
            <p>生成指定大小的图片</p>
        </div>
        
        <div id="main-menu">
            <div class="form-group">
                <label>选择生成类型：</label>
                <select id="imageType">
                    <option value="solid">纯色图片</option>
                    <option value="gradient">渐变图片</option>
                    <option value="transparent">透明图片</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>宽度 (px):</label>
                <input type="number" id="width" value="800" min="1" max="10000">
            </div>
            
            <div class="form-group">
                <label>高度 (px):</label>
                <input type="number" id="height" value="600" min="1" max="10000">
            </div>
            
            <div id="solid-options">
                <div class="form-group">
                    <label>颜色:</label>
                    <input type="color" id="color" value="#4F46E5" class="color-input">
                </div>
            </div>
            
            <div id="gradient-options" class="hidden">
                <div class="form-group">
                    <label>起始颜色:</label>
                    <input type="color" id="startColor" value="#FF6B6B" class="color-input">
                </div>
                <div class="form-group">
                    <label>结束颜色:</label>
                    <input type="color" id="endColor" value="#4F46E5" class="color-input">
                </div>
                <div class="form-group">
                    <label>渐变方向:</label>
                    <select id="direction">
                        <option value="horizontal">水平</option>
                        <option value="vertical">垂直</option>
                        <option value="diagonal">对角线</option>
                        <option value="radial">径向</option>
                    </select>
                </div>
            </div>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="generateImage()">生成图片</button>
                <button class="btn btn-secondary" onclick="resetForm()">重置</button>
            </div>
        </div>
    </div>

    <script>
        // 切换图片类型时显示/隐藏相关选项
        document.getElementById('imageType').addEventListener('change', function() {
            const type = this.value;
            const solidOptions = document.getElementById('solid-options');
            const gradientOptions = document.getElementById('gradient-options');
            
            solidOptions.classList.toggle('hidden', type !== 'solid');
            gradientOptions.classList.toggle('hidden', type !== 'gradient');
        });

        // 生成图片
        function generateImage() {
            const type = document.getElementById('imageType').value;
            const width = parseInt(document.getElementById('width').value);
            const height = parseInt(document.getElementById('height').value);
            
            if (!width || !height || width <= 0 || height <= 0) {
                alert('请输入有效的宽度和高度');
                return;
            }
            
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            
            let filename = '';
            
            switch(type) {
                case 'solid':
                    const color = document.getElementById('color').value;
                    ctx.fillStyle = color;
                    ctx.fillRect(0, 0, width, height);
                    filename = `solid_${width}x${height}_${color.replace('#', '')}.png`;
                    break;
                    
                case 'gradient':
                    const startColor = document.getElementById('startColor').value;
                    const endColor = document.getElementById('endColor').value;
                    const direction = document.getElementById('direction').value;
                    
                    let gradient;
                    switch(direction) {
                        case 'horizontal':
                            gradient = ctx.createLinearGradient(0, 0, width, 0);
                            break;
                        case 'vertical':
                            gradient = ctx.createLinearGradient(0, 0, 0, height);
                            break;
                        case 'diagonal':
                            gradient = ctx.createLinearGradient(0, 0, width, height);
                            break;
                        case 'radial':
                            gradient = ctx.createRadialGradient(width/2, height/2, 0, width/2, height/2, Math.max(width, height)/2);
                            break;
                    }
                    
                    gradient.addColorStop(0, startColor);
                    gradient.addColorStop(1, endColor);
                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, width, height);
                    filename = `gradient_${width}x${height}_${direction}.png`;
                    break;
                    
                case 'transparent':
                    ctx.clearRect(0, 0, width, height);
                    filename = `transparent_${width}x${height}.png`;
                    break;
            }
            
            // 下载图片
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                alert('图片已保存到下载文件夹');
            }, 'image/png');
        }

        // 重置表单
        function resetForm() {
            document.getElementById('width').value = '800';
            document.getElementById('height').value = '600';
            document.getElementById('color').value = '#4F46E5';
            document.getElementById('startColor').value = '#FF6B6B';
            document.getElementById('endColor').value = '#4F46E5';
            document.getElementById('direction').value = 'horizontal';
            document.getElementById('imageType').value = 'solid';
            
            // 触发类型变更事件
            document.getElementById('imageType').dispatchEvent(new Event('change'));
        }
    </script>
</body>
</html>
