# 故障排除指南

## 🔧 解决"未配置功能指令"问题

如果你遇到"未配置功能指令"的提示，请按以下步骤检查：

### 1. 检查文件完整性

确保以下文件都存在且完整：
- ✅ `plugin.json` - 插件配置文件
- ✅ `preload.js` - 预加载脚本
- ✅ `index.html` - 主界面文件
- ✅ `logo.png` - 插件图标

### 2. 验证配置文件

**检查 plugin.json：**
```bash
# 验证JSON语法
python3 -m json.tool plugin.json
```

**检查 preload.js：**
```bash
# 验证JavaScript语法
node -c preload.js
```

### 3. 重新安装插件

1. 在uTools中卸载现有插件
2. 重启uTools
3. 重新安装插件

### 4. 常见问题解决

**问题1：插件无法识别**
- 检查plugin.json中的features配置
- 确保code字段与preload.js中的导出匹配

**问题2：界面无法打开**
- 检查index.html文件是否存在
- 验证HTML语法是否正确

**问题3：图标不显示**
- 确保logo.png文件存在
- 检查文件权限

### 5. 当前配置说明

**plugin.json 配置：**
- 插件名称：图片生成器
- 功能代码：img_generator
- 触发关键词：生成图片、图片生成、图片生成器、img、image

**preload.js 配置：**
- 模式：none（直接跳转到主界面）
- 功能：重定向到index.html

**index.html：**
- 完整的图片生成界面
- 支持纯色、渐变、透明三种图片类型

### 6. 测试步骤

1. **安装插件**
   ```
   uTools设置 → 插件应用 → 开发者 → 安装插件
   ```

2. **测试触发**
   - 在uTools中输入：`生成图片`
   - 应该能看到插件出现在结果中

3. **测试功能**
   - 点击插件进入主界面
   - 设置参数并生成图片
   - 检查图片是否正确下载

### 7. 调试技巧

**查看控制台错误：**
- 在插件界面按F12打开开发者工具
- 查看Console中的错误信息

**检查uTools日志：**
- 查看uTools的错误日志
- 寻找相关的错误信息

### 8. 联系支持

如果以上方法都无法解决问题，请：
1. 收集错误信息和截图
2. 记录操作步骤
3. 提交问题报告

---

**最后更新：** 2025-08-01  
**适用版本：** v1.0.0
