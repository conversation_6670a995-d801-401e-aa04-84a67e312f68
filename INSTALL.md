# 安装说明

## 📦 安装步骤

### 方法一：开发者模式安装（推荐）

1. **下载插件**
   - 下载或克隆此项目到本地
   - 确保所有文件都在同一个文件夹中

2. **打开uTools设置**
   - 启动uTools
   - 按 `Ctrl + ,`（Windows/Linux）或 `Cmd + ,`（macOS）打开设置

3. **进入插件管理**
   - 点击左侧菜单中的"插件应用"
   - 切换到"开发者"选项卡

4. **安装插件**
   - 点击"安装插件"按钮
   - 选择项目文件夹（包含plugin.json的文件夹）
   - 等待安装完成

### 方法二：打包安装

1. **打包插件**
   - 将整个项目文件夹压缩为.zip文件
   - 确保plugin.json在压缩包的根目录

2. **安装插件包**
   - 在uTools中按 `Ctrl + ,` 打开设置
   - 选择"插件应用" → "开发者"
   - 点击"安装插件"，选择.zip文件

## ✅ 验证安装

安装成功后，你可以通过以下方式验证：

1. **检查插件列表**
   - 在uTools设置的"插件应用"中查看是否有"图片生成器"

2. **测试功能**
   - 在uTools主界面输入 `生成图片`
   - 应该能看到插件出现在搜索结果中

## 🚀 开始使用

安装完成后，在uTools中输入以下任一关键词即可启动插件：

- `生成图片`
- `图片生成`
- `img`
- `image`
- `图片生成器`

## 🔧 故障排除

### 常见问题

**Q: 插件安装后找不到？**
A: 
- 检查plugin.json文件是否存在且格式正确
- 确保所有必需文件都在同一目录
- 重启uTools后再试

**Q: 提示"未配置功能指令"？**
A: 
- 检查plugin.json中的features配置
- 确保preload.js文件存在且语法正确
- 查看uTools开发者工具的错误信息

**Q: 插件界面显示异常？**
A: 
- 检查index.html文件是否存在
- 确保logo.png文件存在
- 清除uTools缓存后重新安装

**Q: 生成的图片无法下载？**
A: 
- 检查浏览器下载权限
- 确保有足够的磁盘空间
- 尝试更换下载目录

### 开发者调试

如果遇到问题，可以：

1. **查看控制台**
   - 在插件界面按 `F12` 打开开发者工具
   - 查看Console中的错误信息

2. **检查文件权限**
   - 确保插件文件夹有读取权限
   - 检查下载目录的写入权限

3. **重新安装**
   - 卸载插件后重新安装
   - 清除uTools缓存

## 📞 获取帮助

如果以上方法都无法解决问题，请：

1. 查看项目的README.md文件
2. 提交GitHub Issue（如果有的话）
3. 联系插件作者

## 🔄 更新插件

当有新版本时：

1. 下载新版本文件
2. 覆盖原有文件
3. 在uTools中重新安装插件
4. 重启uTools

---

**注意：** 此插件需要uTools 2.0或更高版本才能正常运行。
