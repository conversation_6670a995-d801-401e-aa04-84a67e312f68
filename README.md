# 图片生成器 - uTools插件

一个简单易用的uTools插件，用于生成指定大小的图片。

## 功能特性

- 🎨 **生成纯色图片** - 创建指定大小和颜色的纯色图片
- 🌈 **生成渐变图片** - 创建多种渐变效果的图片（水平、垂直、对角线、径向）
- 🔍 **生成透明图片** - 创建透明背景的PNG图片
- 📏 **自定义尺寸** - 支持1px到10000px的任意尺寸
- 💾 **一键下载** - 生成的图片自动保存到下载文件夹

## 使用方法

1. 在uTools中输入关键词：`生成图片`、`图片生成`、`img`、`image` 或 `图片生成器`
2. 选择要生成的图片类型
3. 设置图片参数（宽度、高度、颜色等）
4. 点击"生成图片"按钮
5. 图片将自动下载到默认下载文件夹

## 安装方法

1. 下载插件文件
2. 在uTools中按 `Ctrl/Cmd + ,` 打开设置
3. 选择"插件应用"
4. 点击"开发者"选项卡
5. 点击"安装插件"，选择插件文件夹

## 支持的图片格式

- PNG（支持透明背景）

## 系统要求

- uTools 2.0+
- 支持HTML5 Canvas的现代浏览器内核

## 更新日志

### v1.0.0
- 初始版本发布
- 支持纯色图片生成
- 支持渐变图片生成
- 支持透明图片生成

## 许可证

MIT License

## 作者

Your Name

## 反馈与建议

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- Email: <EMAIL>
