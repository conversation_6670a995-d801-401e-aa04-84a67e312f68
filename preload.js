window.exports = {
  "img_generator": {
    mode: "list",
    args: {
      enter: (action, callbackSetList) => {
        callbackSetList([
          {
            title: "生成纯色图片",
            description: "生成指定大小和颜色的纯色图片",
            icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMyIgeT0iMyIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiByeD0iMiIgZmlsbD0iIzRGNDZFNSIvPgo8L3N2Zz4K",
            action: "solid_color"
          },
          {
            title: "生成渐变图片",
            description: "生成指定大小的渐变色图片",
            icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojRkY2QjZCO3N0b3Atb3BhY2l0eToxIiAvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiM0RjQ2RTU7c3RvcC1vcGFjaXR5OjEiIC8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHJlY3QgeD0iMyIgeT0iMyIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiByeD0iMiIgZmlsbD0idXJsKCNncmFkaWVudCkiLz4KPC9zdmc+Cg==",
            action: "gradient"
          },
          {
            title: "生成透明图片",
            description: "生成指定大小的透明PNG图片",
            icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMyIgeT0iMyIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iI0Y1RjVGNSIvPgo8cmVjdCB4PSIxMiIgeT0iMyIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iI0U1RTVFNSIvPgo8cmVjdCB4PSIzIiB5PSIxMiIgd2lkdGg9IjkiIGhlaWdodD0iOSIgZmlsbD0iI0U1RTVFNSIvPgo8cmVjdCB4PSIxMiIgeT0iMTIiIHdpZHRoPSI5IiBoZWlnaHQ9IjkiIGZpbGw9IiNGNUY1RjUiLz4KPC9zdmc+Cg==",
            action: "transparent"
          }
        ]);
      },
      select: (action, itemData) => {
        window.utools.hideMainWindow();
        
        switch(itemData.action) {
          case "solid_color":
            showSolidColorDialog();
            break;
          case "gradient":
            showGradientDialog();
            break;
          case "transparent":
            showTransparentDialog();
            break;
        }
      }
    }
  }
};

// 显示纯色图片生成对话框
function showSolidColorDialog() {
  const html = `
    <div style="padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
      <h2 style="margin-top: 0; color: #333;">生成纯色图片</h2>
      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">宽度 (px):</label>
        <input type="number" id="width" value="800" min="1" max="10000" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">高度 (px):</label>
        <input type="number" id="height" value="600" min="1" max="10000" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="margin-bottom: 20px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">颜色:</label>
        <input type="color" id="color" value="#4F46E5" style="width: 100%; height: 40px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="display: flex; gap: 10px;">
        <button onclick="generateSolidImage()" style="flex: 1; padding: 10px; background: #4F46E5; color: white; border: none; border-radius: 4px; cursor: pointer;">生成图片</button>
        <button onclick="window.utools.hideMainWindow()" style="flex: 1; padding: 10px; background: #6B7280; color: white; border: none; border-radius: 4px; cursor: pointer;">取消</button>
      </div>
    </div>
  `;
  
  window.utools.setExpendHeight(300);
  document.body.innerHTML = html;
}

// 显示渐变图片生成对话框
function showGradientDialog() {
  const html = `
    <div style="padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
      <h2 style="margin-top: 0; color: #333;">生成渐变图片</h2>
      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">宽度 (px):</label>
        <input type="number" id="width" value="800" min="1" max="10000" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">高度 (px):</label>
        <input type="number" id="height" value="600" min="1" max="10000" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">起始颜色:</label>
        <input type="color" id="startColor" value="#FF6B6B" style="width: 100%; height: 40px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">结束颜色:</label>
        <input type="color" id="endColor" value="#4F46E5" style="width: 100%; height: 40px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="margin-bottom: 20px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">渐变方向:</label>
        <select id="direction" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
          <option value="horizontal">水平</option>
          <option value="vertical">垂直</option>
          <option value="diagonal">对角线</option>
          <option value="radial">径向</option>
        </select>
      </div>
      <div style="display: flex; gap: 10px;">
        <button onclick="generateGradientImage()" style="flex: 1; padding: 10px; background: #4F46E5; color: white; border: none; border-radius: 4px; cursor: pointer;">生成图片</button>
        <button onclick="window.utools.hideMainWindow()" style="flex: 1; padding: 10px; background: #6B7280; color: white; border: none; border-radius: 4px; cursor: pointer;">取消</button>
      </div>
    </div>
  `;
  
  window.utools.setExpendHeight(400);
  document.body.innerHTML = html;
}

// 显示透明图片生成对话框
function showTransparentDialog() {
  const html = `
    <div style="padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
      <h2 style="margin-top: 0; color: #333;">生成透明图片</h2>
      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">宽度 (px):</label>
        <input type="number" id="width" value="800" min="1" max="10000" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="margin-bottom: 20px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500;">高度 (px):</label>
        <input type="number" id="height" value="600" min="1" max="10000" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="display: flex; gap: 10px;">
        <button onclick="generateTransparentImage()" style="flex: 1; padding: 10px; background: #4F46E5; color: white; border: none; border-radius: 4px; cursor: pointer;">生成图片</button>
        <button onclick="window.utools.hideMainWindow()" style="flex: 1; padding: 10px; background: #6B7280; color: white; border: none; border-radius: 4px; cursor: pointer;">取消</button>
      </div>
    </div>
  `;
  
  window.utools.setExpendHeight(250);
  document.body.innerHTML = html;
}

// 生成纯色图片
function generateSolidImage() {
  const width = parseInt(document.getElementById('width').value);
  const height = parseInt(document.getElementById('height').value);
  const color = document.getElementById('color').value;

  if (!width || !height || width <= 0 || height <= 0) {
    alert('请输入有效的宽度和高度');
    return;
  }

  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');

  ctx.fillStyle = color;
  ctx.fillRect(0, 0, width, height);

  downloadImage(canvas, `solid_${width}x${height}_${color.replace('#', '')}.png`);
}

// 生成渐变图片
function generateGradientImage() {
  const width = parseInt(document.getElementById('width').value);
  const height = parseInt(document.getElementById('height').value);
  const startColor = document.getElementById('startColor').value;
  const endColor = document.getElementById('endColor').value;
  const direction = document.getElementById('direction').value;

  if (!width || !height || width <= 0 || height <= 0) {
    alert('请输入有效的宽度和高度');
    return;
  }

  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');

  let gradient;

  switch(direction) {
    case 'horizontal':
      gradient = ctx.createLinearGradient(0, 0, width, 0);
      break;
    case 'vertical':
      gradient = ctx.createLinearGradient(0, 0, 0, height);
      break;
    case 'diagonal':
      gradient = ctx.createLinearGradient(0, 0, width, height);
      break;
    case 'radial':
      gradient = ctx.createRadialGradient(width/2, height/2, 0, width/2, height/2, Math.max(width, height)/2);
      break;
  }

  gradient.addColorStop(0, startColor);
  gradient.addColorStop(1, endColor);

  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);

  downloadImage(canvas, `gradient_${width}x${height}_${direction}.png`);
}

// 生成透明图片
function generateTransparentImage() {
  const width = parseInt(document.getElementById('width').value);
  const height = parseInt(document.getElementById('height').value);

  if (!width || !height || width <= 0 || height <= 0) {
    alert('请输入有效的宽度和高度');
    return;
  }

  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');

  // 创建透明背景（不需要填充任何内容）
  ctx.clearRect(0, 0, width, height);

  downloadImage(canvas, `transparent_${width}x${height}.png`);
}

// 下载图片
function downloadImage(canvas, filename) {
  canvas.toBlob(function(blob) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    window.utools.showNotification('图片已保存到下载文件夹');
    window.utools.hideMainWindow();
  }, 'image/png');
}
